"use client";

import { useRealtimeMessage } from "@/hooks/useRealtimeMessage";
import { ChatMessage as ChatMessageType } from "@/services/chatService";
import type {
  ChatMessage as ChatMessage<PERSON><PERSON>,
  ChatRoom,
} from "@/types/chat.types";
import { User } from "@supabase/supabase-js";
import React, { useCallback, useEffect, useRef, useState } from "react";
import ChatInput from "../ChatInput";
import ChatMessage from "../ChatMessage";
import BettingOddsTab from "./BettingOddsTab";

interface DirectChatRoomProps {
  user?: User | null;
  selectedDirectRoomId?: string | null;
  isLoggedIn: boolean;
  directChatRooms: ChatRoom[];
  onOpenAuthModal: (mode: "login" | "register") => void;
  onRoomChange?: (roomId: string) => void;
  loadMessagesForRoom: (roomId: string) => Promise<void>;
  sendMessage: (
    roomId: string,
    content: string
  ) => Promise<{
    error: Error | null;
  }>;
}

export default function DirectChatRoom({
  user,
  isLoggedIn,
  selectedDirectRoomId,
  directChatRooms,
  onOpenAuthModal,
  onRoomChange,
  sendMessage,
}: DirectChatRoomProps) {
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [replyTo, setReplyTo] = useState<ChatMessageType | null>(null);
  const [selectedDirectRoom, setSelectedDirectRoom] = useState<string | null>(
    selectedDirectRoomId ?? ""
  );
  const [activeSubTab, setActiveSubTab] = useState<"chat" | "betting">("chat");

  // Mobile viewport handling
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  const { messages, loading } = useRealtimeMessage(selectedDirectRoom);

  useEffect(() => {
    if (selectedDirectRoomId) {
      setSelectedDirectRoom(selectedDirectRoomId);
    } else if (directChatRooms.length > 0) {
      setSelectedDirectRoom(directChatRooms[0].id);
    }
  }, [directChatRooms, selectedDirectRoomId]);

  // Get user data from context or localStorage as fallback
  const getUserData = useCallback(() => {
    // First try to get from context
    if (user) {
      return {
        userId: user.id,
        name:
          user.user_metadata?.full_name ||
          user.email?.split("@")[0] ||
          "Anonymous",
        email: user.email,
        verified: user.email_confirmed_at ? true : false,
        isAdmin: false, // You can add admin logic here
      };
    }

    // Fallback to localStorage for backward compatibility
    const storedUser = localStorage.getItem("userData");
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (error) {
        // Error parsing user data
      }
    }
    return null;
  }, [user]);

  // Handle viewport and mobile detection
  const updateViewportInfo = useCallback(() => {
    const vh = window.innerHeight;
    const vw = window.innerWidth;
    setViewportHeight(vh);
    setIsMobile(vw < 1024);

    // Detect iOS
    const isIOSDevice =
      /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);

    // Detect keyboard height on mobile
    if (vw < 1024) {
      if (isIOSDevice) {
        // iOS: Use visual viewport to detect keyboard
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          const keyboardHeight = Math.max(
            0,
            window.innerHeight - visualViewport.height
          );
          setKeyboardHeight(keyboardHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      } else {
        // Android: Use window height difference
        const initialHeight = window.visualViewport?.height || vh;
        const currentHeight = window.innerHeight;
        const keyboardHeight = Math.max(0, initialHeight - currentHeight);
        setKeyboardHeight(keyboardHeight);
        setIsKeyboardOpen(keyboardHeight > 0);
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    // Chỉ scroll phần chat, không ảnh hưởng trang chính
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, []);

  // Auto-select first direct chat room when rooms are loaded
  useEffect(() => {
    if (selectedDirectRoomId) {
      setSelectedDirectRoom(selectedDirectRoomId);
    } else if (directChatRooms.length > 0 && !selectedDirectRoom) {
      const firstDirectRoom = directChatRooms[0];
      setSelectedDirectRoom(firstDirectRoom.id);
      onRoomChange?.(firstDirectRoom.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [directChatRooms, selectedDirectRoomId]);

  // Initialize viewport info
  useEffect(() => {
    updateViewportInfo();

    // Listen for viewport changes
    const handleResize = () => updateViewportInfo();
    const handleOrientationChange = () => {
      setTimeout(updateViewportInfo, 100); // Delay to get accurate measurements
    };

    // Listen for visual viewport changes (keyboard on mobile)
    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        updateViewportInfo();
      }
    };

    // iOS specific keyboard events
    const handleIOSKeyboardShow = () => {
      setIsKeyboardOpen(true);
      setTimeout(updateViewportInfo, 100);
    };

    const handleIOSKeyboardHide = () => {
      setIsKeyboardOpen(false);
      setTimeout(updateViewportInfo, 100);
    };

    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleOrientationChange);

    // iOS keyboard events
    if (isIOS) {
      window.addEventListener("focusin", handleIOSKeyboardShow);
      window.addEventListener("focusout", handleIOSKeyboardHide);
    }

    if (window.visualViewport) {
      window.visualViewport.addEventListener(
        "resize",
        handleVisualViewportChange
      );
    }

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleOrientationChange);

      if (isIOS) {
        window.removeEventListener("focusin", handleIOSKeyboardShow);
        window.removeEventListener("focusout", handleIOSKeyboardHide);
      }

      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleVisualViewportChange
        );
      }
    };
  }, [updateViewportInfo, isIOS]);

  // Convert real messages from API format to ChatMessageType format
  const allMessages = React.useMemo(() => {
    const realMessagesConverted = messages.map((msg: ChatMessageApi) => {
      const userData = getUserData();
      const isOwnMessage = userData?.userId === msg.user_id;

      return {
        id: msg.id,
        message: msg.content,
        timestamp: new Date(msg.created_at).getTime(),
        userId: msg.user_id,
        userName: isOwnMessage ? "Bạn" : msg.user?.full_name || "Unknown User",
        userAvatar: msg.user?.avatar_url || "",
        userColor: "bg-blue-500",
        verified: false,
        isAdmin: false,
        replyTo: null,
        reactions: {} as { [key: string]: number },
        pinned: false,
        isAutoMessage: false,
      } as ChatMessageType;
    });

    // Sort by timestamp (oldest first)
    return realMessagesConverted.sort((a, b) => a.timestamp - b.timestamp);
  }, [messages, getUserData]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (allMessages.length > 0) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [allMessages.length]);

  // iOS: Scroll to bottom when keyboard opens
  useEffect(() => {
    if (isIOS && isKeyboardOpen && chatContainerRef.current) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }, 300); // Delay to ensure keyboard is fully open
      return () => clearTimeout(timer);
    }
  }, [isIOS, isKeyboardOpen]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);

      // Check if we can load more messages
      if (scrollTop < 100 && messages.length > 0) {
        // loadOlderMessages();
      }
    }
  }, [messages.length]);

  const handleSubmit = useCallback(
    async (messageText: string, replyToMessage?: ChatMessageType) => {
      if (!messageText.trim() || !isLoggedIn) return;

      const userData = getUserData();
      if (!userData) {
        onOpenAuthModal("login");
        return;
      }

      if (!selectedDirectRoom) {
        return;
      }

      try {
        const { error } = await sendMessage(selectedDirectRoom, messageText);
        if (error) {
          // Failed to send message
        } else {
          // Clear reply state after successful send
          setReplyTo(null);
        }
      } catch (error) {
        // Error sending message
      }
    },
    [isLoggedIn, selectedDirectRoom, getUserData, onOpenAuthModal, sendMessage]
  );

  const renderMessage = useCallback(
    (msg: ChatMessageType) => {
      const userData = getUserData();
      const isOwnMessage = userData?.userId === msg.userId;

      return (
        <ChatMessage
          key={msg.id}
          message={msg}
          isOwnMessage={false} // Always false to show all messages on the left
          onReply={(replyMessage: ChatMessageType) => {
            setReplyTo(replyMessage);
          }}
          onReact={(messageId: string, reactionType: string) => {
            // Handle reaction - you can implement this later
          }}
          onPin={(messageId: string, pinned: boolean) => {
            // Handle pin - you can implement this later
          }}
          onDelete={(messageId: string) => {
            // Handle delete - you can implement this later
          }}
          isAdmin={userData?.isAdmin || false}
        />
      );
    },
    [getUserData]
  );

  // Calculate dynamic styles for mobile
  const getChatContainerStyle = () => {
    if (isMobile && viewportHeight > 0) {
      const inputHeight = 60; // Approximate ChatInput height
      const safeAreaBottom = 20; // Safe area bottom
      const tabsHeight = 50; // Sub-tabs height
      const avatarListHeight = 80; // Avatar list height

      let availableHeight;
      if (isIOS && isKeyboardOpen) {
        // iOS: Use visual viewport height when keyboard is open
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          availableHeight =
            visualViewport.height -
            inputHeight -
            safeAreaBottom -
            tabsHeight -
            (activeSubTab === "chat" ? avatarListHeight : 0);
        } else {
          availableHeight =
            viewportHeight -
            inputHeight -
            safeAreaBottom -
            tabsHeight -
            (activeSubTab === "chat" ? avatarListHeight : 0);
        }
      } else {
        // Android or iOS without keyboard
        availableHeight =
          viewportHeight -
          inputHeight -
          safeAreaBottom -
          keyboardHeight -
          tabsHeight -
          (activeSubTab === "chat" ? avatarListHeight : 0);
      }

      return {
        height: `${Math.max(200, availableHeight)}px`,
        maxHeight: `${availableHeight}px`,
        paddingBottom: "20px",
        // Ensure scroll works on mobile
        overflowY: "auto" as const,
        WebkitOverflowScrolling: "touch" as const,
        overflowX: "hidden" as const,
        position: "relative" as const,
        flex: "1 1 auto",
      };
    }
    // Desktop: Ensure proper height and scroll
    return {
      height: "100%",
      paddingBottom: "20px",
      overflowY: "auto" as const,
    };
  };

  const getChatInputStyle = () => {
    if (isMobile && viewportHeight > 0) {
      let bottomPosition = "0px";

      if (isIOS && isKeyboardOpen) {
        // iOS: Position above keyboard using visual viewport
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          bottomPosition = `${window.innerHeight - visualViewport.height}px`;
        }
      } else if (keyboardHeight > 0) {
        // Android: Use keyboard height
        bottomPosition = `${keyboardHeight}px`;
      }

      return {
        position: "fixed" as const,
        bottom: bottomPosition,
        left: "0",
        right: "0",
        zIndex: 50,
        borderTop: "1px solid var(--chat-input-border, #e5e7eb)",
        paddingBottom: "env(safe-area-inset-bottom, 10px)",
        boxShadow: "0 -2px 10px rgba(0, 0, 0, 0.1)",
      };
    }
    return {};
  };

  // Handle room selection for direct chat
  const handleRoomSelect = (roomId: string) => {
    setSelectedDirectRoom(roomId);
    onRoomChange?.(roomId);
  };

  return (
    <div
      className="flex flex-col h-full relative"
      style={{
        paddingBottom: isMobile ? "0px" : "env(safe-area-inset-bottom, 0px)",
        // Mobile: Ensure proper height and allow scrolling
        ...(isMobile
          ? {
              position: "relative",
              height: "100%",
              overflow: "hidden", // Let child container handle scrolling
            }
          : {}),
      }}
    >
      {/* Sub-tabs for Direct Chat */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex">
          <button
            onClick={() => setActiveSubTab("chat")}
            className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
              activeSubTab === "chat"
                ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
            }`}
          >
            Chat riêng
          </button>
          <button
            onClick={() => setActiveSubTab("betting")}
            className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
              activeSubTab === "betting"
                ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
            }`}
          >
            Soi kèo
          </button>
        </div>
      </div>

      {/* Avatar List for Direct Chat */}
      {activeSubTab === "chat" && directChatRooms.length > 0 && (
        <div className="p-2 border-b border-gray-200 dark:border-gray-700">
          <div className="flex gap-2 sm:gap-3 overflow-x-auto pb-1 scrollbar-hide scroll-smooth">
            {directChatRooms.map((room) => {
              const otherUser = room.other_user;
              const isSelected = selectedDirectRoom === room.id;

              return (
                <div
                  key={room.id}
                  onClick={() => handleRoomSelect(room.id)}
                  className={`flex-shrink-0 flex flex-col items-center cursor-pointer transition-all duration-200 min-w-0 ${
                    isSelected
                      ? "transform scale-105"
                      : "hover:transform hover:scale-105"
                  }`}
                >
                  <div
                    className={`relative w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden border-2 transition-colors ${
                      isSelected
                        ? "border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800"
                        : "border-gray-300 dark:border-gray-600 hover:border-blue-400"
                    }`}
                  >
                    {otherUser?.avatar_url ? (
                      <img
                        src={otherUser.avatar_url}
                        alt={otherUser.full_name || "User"}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                        {(otherUser?.full_name || room.name || "U")
                          .charAt(0)
                          .toUpperCase()}
                      </div>
                    )}
                    {/* Online indicator */}
                    <div className="absolute bottom-0 right-0 w-2.5 h-2.5 sm:w-3 sm:h-3 bg-green-500 border border-white dark:border-gray-800 rounded-full"></div>
                    {/* Unread message count badge */}
                    {room.unread_count && room.unread_count > 0 && (
                      <div className="absolute -top-0.5 -right-0.5 w-4 h-4 sm:w-5 sm:h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                        {room.unread_count > 99 ? "99+" : room.unread_count}
                      </div>
                    )}
                  </div>
                  <div className="mt-1 text-center w-full px-1">
                    <p
                      className={`text-xs whitespace-nowrap ${
                        isSelected
                          ? "text-blue-600 dark:text-blue-400 font-medium"
                          : "text-gray-600 dark:text-gray-400"
                      }`}
                      title={otherUser?.full_name || room.name || "User"}
                    >
                      {otherUser?.full_name || room.name || "User"}
                    </p>
                    {isSelected && (
                      <div className="w-0.5 h-0.5 bg-blue-500 rounded-full mx-auto mt-0.5"></div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Soi kèo Content */}
      {activeSubTab === "betting" && <BettingOddsTab />}

      {/* Messages Container */}
      {activeSubTab === "chat" && (
        <div
          ref={chatContainerRef}
          data-chat-container
          className="flex-1 overflow-y-auto py-2 px-2 space-x-2 sm:space-x-2 space-y-2 sm:space-y-3 scrollbar-hide"
          style={getChatContainerStyle()}
          onScroll={handleScroll}
        >
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : allMessages.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>Chưa có tin nhắn nào. Hãy bắt đầu cuộc trò chuyện!</p>
            </div>
          ) : (
            allMessages.map(renderMessage)
          )}
          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Scroll to bottom button */}
      {showScrollToBottom && activeSubTab === "chat" && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-20 right-4 bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-all duration-200 z-10"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        </button>
      )}

      {/* Chat Input */}
      {activeSubTab === "chat" && (
        <div
          className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
          style={getChatInputStyle()}
        >
          <ChatInput
            onSubmit={handleSubmit}
            isLoggedIn={isLoggedIn}
            onOpenAuthModal={onOpenAuthModal}
            replyTo={replyTo}
            onCancelReply={() => setReplyTo(null)}
            placeholder="Nhập tin nhắn..."
          />
        </div>
      )}
    </div>
  );
}
