import { useState, useCallback, useEffect } from 'react';
import { MatchData } from '@/types/match';
import { fetchMatches } from '@/services/matchService';

// Interface for Google Sheets order data
interface OrderData {
  id?: string;
  ID?: string;
  match_id?: string;
  order?: string | number;
  [key: string]: unknown;
}

// Interface for the hook return
interface UseMatchOrderMappingReturn {
  loading: boolean;
  error: string | null;
  hasOrder: (matchId: string) => boolean;
  refreshOrderData: () => Promise<void>;
  clearError: () => void;
}

// Configuration
const SPREADSHEET_ID = '1Xw1IpIyjMtuIfbWlJ9kyiwioUoYMgRCydayKBI93j3Y';
const ORDER_SHEET_RANGE = 'Order!A1:G';

export const useMatchOrderMapping = (): UseMatchOrderMappingReturn => {
  const [orderData, setOrderData] = useState<OrderData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch Google Sheets data
  const fetchGoogleSheetsData = useCallback(async (spreadsheetId: string, range: string): Promise<OrderData[]> => {
    const response = await fetch(`/api/google-sheets?spreadsheetId=${spreadsheetId}&range=${range}`);
    const result = await response.json();
    
    if (!result.success || !result.data?.values) {
      throw new Error('Không thể lấy dữ liệu từ Google Sheets');
    }
    
    return result.data.values as OrderData[];
  }, []);

  // Refresh order data from Google Sheets
  const refreshOrderData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const sheetData = await fetchGoogleSheetsData(SPREADSHEET_ID, ORDER_SHEET_RANGE);
      
      // Filter records that have order value
      const orderedData = sheetData.filter((item: OrderData) => {
        const order = item.order;
        return order !== undefined && order !== null && order !== '' && order !== '0';
      });

      setOrderData(orderedData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch order data';
      setError(errorMessage);
      setOrderData([]);
    } finally {
      setLoading(false);
    }
  }, [fetchGoogleSheetsData]);

  // Check if a match has order
  const hasOrder = useCallback((matchId: string): boolean => {
    if (!matchId || orderData.length === 0) {
      return false;
    }

    return orderData.some((item: OrderData) => {
      const itemId = item.id || item.ID || item.match_id;
      return itemId === matchId;
    });
  }, [orderData]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-fetch order data on mount
  useEffect(() => {
    refreshOrderData();
  }, [refreshOrderData]);

  return {
    loading,
    error,
    hasOrder,
    refreshOrderData,
    clearError,
  };
};

export default useMatchOrderMapping;
