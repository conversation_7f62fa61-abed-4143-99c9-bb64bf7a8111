import { useMemo } from 'react';
import { MatchData } from '@/types/match';
import { type FilterType } from '@/constants/filters';
import { useMatches } from '@/hooks/useMatches';
import { useMatchOrderMapping } from '@/hooks/useMatchOrderMapping';

// Extended MatchData with order information
export interface MatchDataWithOrder extends MatchData {
  hasOrder: boolean;
}

interface UseMatchesWithOrderReturn {
  matches: MatchDataWithOrder[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  currentFilter: FilterType;
  filterCounts: Record<FilterType, number>;
  orderLoading: boolean;
  orderError: string | null;
  fetchMatchesByFilter: (filter: FilterType, category?: string) => Promise<void>;
  fetchAllCounts: (category?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  refreshOrderData: () => Promise<void>;
  clearError: () => void;
  clearOrderError: () => void;
}

export const useMatchesWithOrder = (): UseMatchesWithOrderReturn => {
  // Use the base matches hook
  const {
    matches: baseMatches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  } = useMatches();

  // Use the order mapping hook
  const {
    loading: orderLoading,
    error: orderError,
    hasOrder,
    getOrderValue,
    refreshOrderData,
    clearError: clearOrderError,
  } = useMatchOrderMapping();

  // Combine matches with order information and sort by order priority
  const matches = useMemo((): MatchDataWithOrder[] => {
    const matchesWithOrder = baseMatches.map((match) => ({
      ...match,
      hasOrder: hasOrder(match.id),
    }));

    // Sort matches: by order value (ascending), then non-order matches
    return matchesWithOrder.sort((a, b) => {
      const aOrderValue = getOrderValue(a.id);
      const bOrderValue = getOrderValue(b.id);

      // If both have order values, sort by order number (ascending)
      if (aOrderValue !== null && bOrderValue !== null) {
        return aOrderValue - bOrderValue;
      }

      // If one has order and the other doesn't, prioritize the one with order
      if (aOrderValue !== null && bOrderValue === null) return -1;
      if (aOrderValue === null && bOrderValue !== null) return 1;

      // If both don't have order, maintain original order
      return 0;
    });
  }, [baseMatches, hasOrder, getOrderValue]);

  return {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    orderLoading,
    orderError,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    refreshOrderData,
    clearError,
    clearOrderError,
  };
};

export default useMatchesWithOrder;
