import { useState, useCallback, useEffect, useMemo } from 'react';
import { MatchData } from '@/types/match';
import { FILTER_TYPES, type FilterType } from '@/constants/filters';
import { useMatches } from '@/hooks/useMatches';
import { useMatchOrderMapping } from '@/hooks/useMatchOrderMapping';

// Extended MatchData with order information
export interface MatchDataWithOrder extends MatchData {
  hasOrder: boolean;
}

interface UseMatchesWithOrderReturn {
  matches: MatchDataWithOrder[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  currentFilter: FilterType;
  filterCounts: Record<FilterType, number>;
  orderLoading: boolean;
  orderError: string | null;
  fetchMatchesByFilter: (filter: FilterType, category?: string) => Promise<void>;
  fetchAllCounts: (category?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  refreshOrderData: () => Promise<void>;
  clearError: () => void;
  clearOrderError: () => void;
}

export const useMatchesWithOrder = (): UseMatchesWithOrderReturn => {
  // Use the base matches hook
  const {
    matches: baseMatches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  } = useMatches();

  // Use the order mapping hook
  const {
    loading: orderLoading,
    error: orderError,
    hasOrder,
    refreshOrderData,
    clearError: clearOrderError,
  } = useMatchOrderMapping();

  // Combine matches with order information
  const matches = useMemo((): MatchDataWithOrder[] => {
    return baseMatches.map((match) => ({
      ...match,
      hasOrder: hasOrder(match.id),
    }));
  }, [baseMatches, hasOrder]);

  return {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    orderLoading,
    orderError,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    refreshOrderData,
    clearError,
    clearOrderError,
  };
};

export default useMatchesWithOrder;
