"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useChatService } from "@/hooks/useChatService";
import { ChatRoom } from "@/types";
import { User } from "@supabase/supabase-js";
import { useRouter } from "next/navigation";

interface Expert {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar_url: string | null;
}

interface BannerData {
  id: string;
  expert: Expert;
  tournament: string;
  team_name: string;
  closing_bet: string;
  saying: string;
  status: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  sent_at: string | null;
  type: string;
}

interface BannerOddsProps {
  currentBanner: BannerData;
  onBannerClick?: () => void;
}

export default function BannerOdds({
  currentBanner,
  onBannerClick,
}: BannerOddsProps) {
  // Handle banner click to create 1-1 chat with admin

  if (!currentBanner) {
    return null;
  }

  return (
    <div
      className={`absolute top-0 left-0 right-0 z-10 transition-all duration-700 ease-out cursor-pointer opacity-100 translate-y-0`}
      onClick={onBannerClick}
    >
      {/* Banner sử dụng hình ảnh 5.png và 6.png từ banner-chat */}
      <div className="relative overflow-hidden">
        {/* Hình ảnh chính 5.png */}
        <img
          src="/banner-chat/5.png"
          alt="Banner Background"
          className="w-full h-20 object-cover"
        />

        {/* Hình ảnh phụ 6.png - nằm dưới, chiều cao bằng 1/3 */}
        <img
          src="/banner-chat/6.png"
          alt="Banner Secondary"
          className="w-full h-7 object-cover"
        />

        {/* Banner overlay trên hình 6.png */}
        <div className="absolute bottom-0 left-0 right-0 h-7 flex items-center">
          {/* Phần bên trái - Không màu nền với icon chat */}
          <div className="flex-[0.6] h-full flex items-center px-3 gap-2">
            <img
              src="/banner-chat/3.png"
              alt="Chat Icon"
              className="w-5 h-5 object-contain ml-1"
            />
            <span className="text-white text-[13px] font-medium">
              {currentBanner.saying || "Tin nhắn"}
            </span>
          </div>

          {/* Phần bên phải - Màu đỏ đậm với icon type */}
          <div className="flex-[0.4] bg-red-800 h-7 flex items-center px-3 gap-2">
            <img
              src="/banner-chat/4.png"
              alt="Hand Icon"
              className="w-4 h-4 object-contain"
            />
            <span className="text-white text-[11px] font-bold uppercase text-center">
              {currentBanner.type || "KÈO VIP"}
            </span>
          </div>
        </div>

        {/* Overlay thông tin banner - chỉ trong phạm vi hình 5 */}
        <div className="absolute top-0 left-0 right-0 h-20 bg-black/30 flex items-center px-1">
          {/* Phần bên trái - Avatar, status, tên (flex-3) */}
          <div
            className="flex-3 flex items-center pl-0"
            style={{ width: "40%" }}
          >
            {/* Avatar */}
            <div className="relative" style={{ marginLeft: "10px" }}>
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center overflow-hidden">
                {currentBanner.expert.avatar_url ? (
                  <img
                    src={currentBanner.expert.avatar_url}
                    alt="Expert Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <img
                    src="/banner-chat/1.png"
                    alt="Default Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                )}
              </div>
              {/* Chấm trạng thái hoạt động với animation pulse */}
              <div className="absolute -bottom-0 -right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
            </div>

            {/* Tên và trạng thái */}
            <div className="flex flex-col ml-1 min-w-0">
              <span className="text-white font-bold text-xs drop-shadow-lg truncate">
                {currentBanner.expert.name || "AD Long Thiên"}
              </span>
              <span className="text-white/80 text-xs drop-shadow-lg truncate">
                Đang hoạt động
              </span>
            </div>
          </div>

          {/* Phần giữa - Thông tin trận đấu (flex-4) */}
          <div
            className="flex-4 flex flex-col items-center text-left px-1 relative"
            style={{ width: "45%" }}
          >
            <div
              className={`text-white/90 text-xs font-medium drop-shadow-lg mb-1 transition-opacity duration-300 ${
                currentBanner.status?.trim() === "win"
                  ? "opacity-30"
                  : "opacity-90"
              }`}
            >
              {currentBanner.tournament || "BÓNG ĐÁ - GIẢI LIGUE 1 PHÁP [FT]"}
            </div>
            <div
              className={`text-yellow-400 font-bold text-sm drop-shadow-lg transition-opacity duration-300 ${
                currentBanner.status?.trim() === "win"
                  ? "opacity-30"
                  : "opacity-100"
              }`}
            >
              {currentBanner.team_name || "LORIENT (H) vs STADE RENNAIS"}
            </div>
            {/* Hiển thị image keo_thang.png khi status là THẮNG */}
            {currentBanner.status?.trim() === "win" && (
              <img
                src="/banner-chat/2.png"
                alt="Kèo Thắng"
                className="absolute inset-0 w-full h-full object-contain opacity-90 transform -rotate-12 origin-center"
              />
            )}
          </div>

          {/* Phần bên phải - Thông tin kèo (flex-3) */}
          <div
            className="flex-3 flex flex-col items-center justify-center"
            style={{ width: "20%" }}
          >
            <span
              className={`text-green-400 font-bold text-lg drop-shadow-lg transition-opacity duration-300 ${
                currentBanner.status?.trim() === "win"
                  ? "opacity-30"
                  : "opacity-100"
              }`}
            >
              {currentBanner.closing_bet?.split(" ")[0] || ""}
            </span>
            <span
              className={`text-green-400 font-bold text-sm drop-shadow-lg transition-opacity duration-300 ${
                currentBanner.status?.trim() === "win"
                  ? "opacity-30"
                  : "opacity-100"
              }`}
            >
              {currentBanner.closing_bet?.split(" ").slice(1).join(" ") || ""}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
