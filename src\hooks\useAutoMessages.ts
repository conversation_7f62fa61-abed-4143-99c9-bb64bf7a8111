import { useCallback, useEffect, useRef } from "react";

import { ChatMessage } from "../services/chatService";

interface AutoMessageConfig {
  matchId: string;
  isEnabled: boolean;
  onAddMessage?: (message: ChatMessage) => void;
}

const SYSTEM_USER = {
  id: "system-auto",
  name: "<PERSON><PERSON> thống",
  avatar: "/ngoaihangtv.png",
  color: "#FF6B35",
};

const RANDOM_NAMES = [
  "MaxDowman",
  "CuongQuoc99",
  "VuotBien123",
  "<PERSON>bie12",
  "Crisseven7",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON>ang<PERSON>ota88",
  "DTK09",
  "<PERSON><PERSON>ung<PERSON><PERSON>",
  "<PERSON>dq<PERSON><PERSON><PERSON><PERSON>",
  "David<PERSON><PERSON><PERSON>ue<PERSON>",
  "KingIbra10",
  "Son<PERSON>ro98",
  "Hieudz123",
  "LuanNguyen99",
  "Kaka2002",
  "<PERSON><PERSON><PERSON><PERSON>97",
  "<PERSON><PERSON><PERSON>e88",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>99",
  "VanVo2000",
  "<PERSON><PERSON><PERSON>123",
  "<PERSON><PERSON><PERSON><PERSON>09",
  "<PERSON>Khang98",
  "LamDz99",
  "<PERSON>rongT<PERSON>88",
  "<PERSON>hNien09",
  "CuongDepTrai",
  "HuuNghia97",
  "HNPro123",
  "Lequy99",
  "DatVipPro",
  "LongKen1999",
  "KhoiDepzai",
  "BaoTran123",
  "<PERSON>yHoang98",
  "TrungKien09",
  "ManhHung123",
  "VietAnh97",
  "NgocLan88",
  "AnhThu99",
  "KieuOanh97",
  "LanAnh2001",
  "QuynhTrang09",
  "MyLinh123",
  "HoaiThu99",
  "AnhTuan97",
  "SonTung09",
  "MinhVu123",
];

const RANDOM_MESSAGES = [
  "chuyển người chậm là mất nhịp",
  "Siuuu nhẹ đầu trận",
  "nem bien xa coi có ăn không",
  "xiuu nhe neu chot som hon",
  "doi khach khong giu duoc cu ly",
  "70 rồi mà khóa chưa mở",
  "Press cao hơi ngộp nha",
  "Dự đoán cuối trận có pen không",
  "doi ban khoét khoảng trống sau lưng hậu vệ",
  "Thủ môn bên kia bắt dính ghê",
  "Thay người đúng lúc quá",
  "goc ben phai lặp lại khá nhiều",
  " khung thanh hom nay như bị yểm",
  "motm mình chọn số 10",
  "xiuuu nếu cắt được mặt",
  "Đường chuyển trạng thái nhanh nhìn đã",
  "huớng tấn công lệch trái khá nhiều",
  "Thêm tiền vệ trụ cho chắc",
  "Đường chọc khe xuyên tuyến quá ngọt",
  "pass key là bú nết",
  "khán giả có nhiệt dữ",
  "thủ môn bên kia phản xạ tốt",
  "Đến phút 88 rồi tim đập nhanh",
  "Tốc độ đầu trận căng thật",
  "xà cứu một bàn rồi",
  "chuyen sai liền tay cần bình tĩnh",
  "không bet chỉ coi cho vui",
  "Bú cánh trái hơi mạnh nha",
  "pha đó sớm nửa nhịp là siuuu",
  "doi khach xe bus khá kín",
  "pha đó nếu chụp nhanh là xong",
  "đường căng ngang cut back hợp lý hơn",
  "phut 90+4 rồi sắp hết giờ",
  "nem bien xa chuẩn ghê",
  "đổi cánh liên tục khó chịu",
  "doi nha đang húp cánh phải",
  "Chọc khe này mượt như bơ",
  "hup nhe pha rebound kia",
  "VAR vào chưa mọi người",
  "xem mà toát mồ hôi tay",
  "pass hơi non nên mất bóng",
  "mn có ai cũng run tay không",
  "chuyền về thủ môn hơi liều",
  "chạy gắt quá nên chuyền lỗi nhiều",
  "phạt góc bên trái coi sao",
  "goc nua coi co gi hot",
  "Giữ bóng đi rồi tính",
  "cánh trái của ta đang húp",
  "Đá 4 4 2 chưa",
  "Góc số 6 nhen",
  "mai coi tiếp nhe",
  "80 rồi tăng tốc không",
  "Hết trận rồi tạm biệt mn",
  "hup nốt pha bóng chết kia",
  "doi khach ve đội hình khá thấp",
  "phạt bóng lên hơi vội",
  "doi kia hơi xuống sức",
  "doi nha húp pos nhiều quá",
  "chạy như này dễ hụt hơi",
  "doi ban dang hup counter",
  "Thủ môn như có nam châm",
  "ai MOTM tạm thời",
  "Đổi người phút 60 được không",
  "ai đoán 1 0 xịn ghê",
  "phạt góc này có mùi",
  "90+2 rồi giữ điểm cái đã",
  "Góc nữa kìa mn",
  "siuuu nếu bóng qua chân số 7",
  "Trung vệ lên tham gia đánh đầu được",
  "tr trong tài nay thổi khá tay",
  "Đấu tay đôi căng thật",
  "phat bong len thong minh hon roi",
  "Giữ tinh thần fair play nha mn",
  "Cánh phải nóng quá rồi",
  "fan trung lập xem cũng phê",
  "tranh chấp giữa sân gắt thật",
  "xut xa di ban oi siuuu",
  "fan sân nhà hôm nay sung ghê",
  "doi kia dap gấp hơi mệt",
  "giu nhip thôi đừng nôn",
  "vao nao ae siuuu",
  "doi nha phoi hop một chạm đẹp",
  "chuyền 1 chạm mượt phết",
  "var check xíu nhe",
  "giữ hòa khí nha mọi người",
  "chạy chỗ thông minh thiệt",
  "Phạt lệ tuyến hai thử xem",
  "Cánh phải của họ để hở nhiều",
  "xiuu đầu hiệp 2 vào nhiệt liền",
  "highlight đêm nay chắc dài",
  "phan cong 3 người chạy như gió",
  "chạm nhẹ là ra bàn liền",
  "var vào là lại căng",
  "chạy cánh kiểu này là bú liền",
  "huan luyen vien đọc bài ổn phết",
  "doi nha can 1 khoảnh khắc tỏa sáng",
  "pha vặn sườn đẹp thiệt",
  "0 0 mà nhịp nhanh phết",
  "Bẫy việt vị chưa chuẩn lắm",
  "xem chill thôi nhe ko all in",
];

const JOIN_MESSAGES = ["đã tham gia phòng chat!"];

function getRandomName(): string {
  return RANDOM_NAMES[Math.floor(Math.random() * RANDOM_NAMES.length)];
}

function getRandomMessage(): string {
  return RANDOM_MESSAGES[Math.floor(Math.random() * RANDOM_MESSAGES.length)];
}

function getRandomJoinMessage(): string {
  return JOIN_MESSAGES[Math.floor(Math.random() * JOIN_MESSAGES.length)];
}

function createMessage(isJoinMessage = false, isPromoMessage = false) {
  // Tạo timestamp ngẫu nhiên trong khoảng 0-30 giây trước thời điểm hiện tại
  // để tin nhắn tự động trộn lẫn với tin nhắn thật
  const randomDelay = Math.floor(Math.random() * 30000); // 0-30 seconds
  const mixedTimestamp = Date.now() - randomDelay;

  if (isPromoMessage) {
    return {
      id: `promo-${Date.now()}-${Math.random()}`,
      userId: "system-promo",
      userName: "NGOẠI HẠNG TV",
      userAvatar: "/ngoaihangtv.png",
      userColor: "#1e40af",
      message:
        "🔥 HOT chỉ có tại NGOẠI HẠNG TV - KUDV liên hệ CSKH để nhận ngay khuyến mãi nạp đầu lên tới 1688k. Quà tặng tân thủ lên tới 10 triệu. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu. ZALO: <a href='https://zalo.me/0994190627' target='_blank' rel='noopener noreferrer' style='color: #FFD700; text-decoration: underline; font-weight: bold;'>https://zalo.me/0994190627</a> - TELEGRAM: <a href='https://t.me/thuphuongepl' target='_blank' rel='noopener noreferrer' style='color: #FFD700; text-decoration: underline; font-weight: bold;'>https://t.me/thuphuongepl</a> 🎁",
      timestamp: mixedTimestamp,
      verified: true,
      reactions: {},
      isAutoMessage: true,
      isPromoMessage: true,
      isJoinMessage: false,
    };
  }

  const randomName = getRandomName();
  const content = isJoinMessage
    ? `${randomName} ${getRandomJoinMessage()}`
    : getRandomMessage();

  return {
    id: `msg_${Date.now()}_${Math.random()}`,
    userId: `user_${Math.random()}`,
    userName: randomName,
    userAvatar: "/ngoaihangtv.png",
    userColor: "bg-blue-500",
    message: content,
    timestamp: mixedTimestamp,
    verified: Math.random() > 0.7,
    reactions: {},
    isAutoMessage: true,
    isJoinMessage: isJoinMessage,
  };
}

export function useAutoMessages({
  matchId,
  isEnabled,
  onAddMessage,
}: AutoMessageConfig) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const promoTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const joinTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isActiveRef = useRef(false);

  // Function to add regular messages to chat display
  const addRegularMessage = useCallback(() => {
    if (!isActiveRef.current || !onAddMessage) return;

    const regularMessage = createMessage(false, false);
    onAddMessage(regularMessage);

    // Schedule next regular message with random interval between 1-2 minutes
    if (isActiveRef.current) {
      const minInterval = 60000; // 1 minute
      const maxInterval = 120000; // 2 minutes
      const nextInterval =
        Math.floor(Math.random() * (maxInterval - minInterval + 1)) +
        minInterval;
      timeoutRef.current = setTimeout(addRegularMessage, nextInterval);
    }
  }, [onAddMessage]);

  // Function to add join messages
  const addJoinMessage = useCallback(() => {
    if (!isActiveRef.current || !onAddMessage) return;

    const joinMessage = createMessage(true, false);
    onAddMessage(joinMessage);

    // Schedule next join message with random interval between 30s-60s
    if (isActiveRef.current) {
      const minInterval = 30000; // 30 seconds
      const maxInterval = 60000; // 60 seconds
      const nextInterval =
        Math.floor(Math.random() * (maxInterval - minInterval + 1)) +
        minInterval;
      joinTimeoutRef.current = setTimeout(addJoinMessage, nextInterval);
    }
  }, [onAddMessage]);

  // Function to add promo messages
  const addPromoMessage = useCallback(() => {
    if (!isActiveRef.current || !onAddMessage) return;

    const promoMessage = createMessage(false, true);
    onAddMessage(promoMessage);

    // Schedule next promo message with random interval between 2-5 minutes
    if (isActiveRef.current) {
      const minInterval = 120000; // 2 minutes
      const maxInterval = 300000; // 5 minutes
      const nextInterval =
        Math.floor(Math.random() * (maxInterval - minInterval + 1)) +
        minInterval;
      promoTimeoutRef.current = setTimeout(addPromoMessage, nextInterval);
    }
  }, [onAddMessage]);

  // Start auto messages
  const startAutoMessages = useCallback(() => {
    if (!isEnabled || isActiveRef.current) return;

    isActiveRef.current = true;

    // Start regular messages after 10-20 seconds (for testing, will change back to 1-2 minutes)
    const regularMinDelay = 60000; // 60 seconds
    const regularMaxDelay = 120000; // 120 seconds
    const regularRandomDelay =
      Math.floor(Math.random() * (regularMaxDelay - regularMinDelay + 1)) +
      regularMinDelay;
    timeoutRef.current = setTimeout(addRegularMessage, regularRandomDelay);

    // Start join messages after 30s-60s
    const joinMinDelay = 30000; // 30 seconds
    const joinMaxDelay = 60000; // 60 seconds
    const joinRandomDelay =
      Math.floor(Math.random() * (joinMaxDelay - joinMinDelay + 1)) +
      joinMinDelay;
    joinTimeoutRef.current = setTimeout(addJoinMessage, joinRandomDelay);

    // Start promo messages after 2-5 minutes
    const promoMinDelay = 120000; // 2 minutes
    const promoMaxDelay = 300000; // 5 minutes
    const promoRandomDelay =
      Math.floor(Math.random() * (promoMaxDelay - promoMinDelay + 1)) +
      promoMinDelay;
    promoTimeoutRef.current = setTimeout(addPromoMessage, promoRandomDelay);
  }, [isEnabled, addRegularMessage, addJoinMessage, addPromoMessage]);

  // Stop auto messages
  // Stop auto messages
  const stopAutoMessages = useCallback(() => {
    isActiveRef.current = false;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (joinTimeoutRef.current) {
      clearTimeout(joinTimeoutRef.current);
      joinTimeoutRef.current = null;
    }

    if (promoTimeoutRef.current) {
      clearTimeout(promoTimeoutRef.current);
      promoTimeoutRef.current = null;
    }
  }, []);

  // Effect to handle enable/disable
  useEffect(() => {
    if (isEnabled) {
      startAutoMessages();
    } else {
      stopAutoMessages();
    }

    return () => {
      stopAutoMessages();
    };
  }, [isEnabled, startAutoMessages, stopAutoMessages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAutoMessages();
      if (promoTimeoutRef.current) {
        clearTimeout(promoTimeoutRef.current);
        promoTimeoutRef.current = null;
      }
      if (joinTimeoutRef.current) {
        clearTimeout(joinTimeoutRef.current);
        joinTimeoutRef.current = null;
      }
    };
  }, [stopAutoMessages]);

  return {
    startAutoMessages,
    stopAutoMessages,
    isActive: isActiveRef.current,
  };
}
